{ TARGET_OPENBSD_NR___getcwd, "__getcwd", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR___semctl, "__semctl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR___syscall, "__syscall", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR___sysctl, "__sysctl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_accept, "accept", "%s(%d,%#x,%#x)", NULL, NULL },
{ TARGET_OPENBSD_NR_access, "access", "%s(\"%s\",%#o)", NULL, NULL },
{ TARGET_OPENBSD_NR_acct, "acct", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_adjfreq, "adjfreq", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_adjtime, "adjtime", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_bind, "bind", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_break, "break", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_chdir, "chdir", "%s(\"%s\")", NULL, NULL },
{ TARGET_OPENBSD_NR_chflags, "chflags", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_chmod, "chmod", "%s(\"%s\",%#o)", NULL, NULL },
{ TARGET_OPENBSD_NR_chown, "chown", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_chroot, "chroot", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_clock_getres, "clock_getres", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_clock_gettime, "clock_gettime", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_clock_settime, "clock_settime", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_close, "close", "%s(%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_closefrom, "closefrom", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_connect, "connect", "%s(%d,%#x,%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_dup, "dup", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_dup2, "dup2", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_execve, "execve", NULL, print_execve, NULL },
{ TARGET_OPENBSD_NR_exit, "exit", "%s(%d)\n", NULL, NULL },
{ TARGET_OPENBSD_NR_fchdir, "fchdir", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_fchflags, "fchflags", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_fchmod, "fchmod", "%s(%d,%#o)", NULL, NULL },
{ TARGET_OPENBSD_NR_fchown, "fchown", "%s(\"%s\",%d,%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_fcntl, "fcntl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_fhopen, "fhopen", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_fhstat, "fhstat", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_fhstatfs, "fhstatfs", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_flock, "flock", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_fork, "fork", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_fpathconf, "fpathconf", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_fstat, "fstat", "%s(%d,%p)", NULL, NULL },
{ TARGET_OPENBSD_NR_fstatfs, "fstatfs", "%s(%d,%p)", NULL, NULL },
{ TARGET_OPENBSD_NR_fsync, "fsync", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_ftruncate, "ftruncate", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_futimes, "futimes", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getdirentries, "getdirentries", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getegid, "getegid", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_geteuid, "geteuid", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_getfh, "getfh", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getfsstat, "getfsstat", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getgid, "getgid", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_getgroups, "getgroups", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getitimer, "getitimer", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getlogin, "getlogin", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getpeereid, "getpeereid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getpeername, "getpeername", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getpgid, "getpgid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getpgrp, "getpgrp", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_getpid, "getpid", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_getppid, "getppid", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_getpriority, "getpriority", "%s(%#x,%#x)", NULL, NULL },
{ TARGET_OPENBSD_NR_getresgid, "getresgid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getresuid, "getresuid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getrlimit, "getrlimit", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getrusage, "getrusage", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getsid, "getsid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getsockname, "getsockname", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getsockopt, "getsockopt", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getthrid, "getthrid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_gettimeofday, "gettimeofday", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_getuid, "getuid", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_ioctl, "ioctl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_issetugid, "issetugid", "%s()", NULL, NULL },
{ TARGET_OPENBSD_NR_kevent, "kevent", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_kill, "kill", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_kqueue, "kqueue", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_ktrace, "ktrace", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_lchown, "lchown", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_lfs_bmapv, "lfs_bmapv", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_lfs_markv, "lfs_markv", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_lfs_segclean, "lfs_segclean", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_lfs_segwait, "lfs_segwait", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_link, "link", "%s(\"%s\",\"%s\")", NULL, NULL },
{ TARGET_OPENBSD_NR_listen, "listen", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_lseek, "lseek", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_lstat, "lstat", "%s(\"%s\",%p)", NULL, NULL },
{ TARGET_OPENBSD_NR_madvise, "madvise", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_mincore, "mincore", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_minherit, "minherit", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_mkdir, "mkdir", "%s(\"%s\",%#o)", NULL, NULL },
{ TARGET_OPENBSD_NR_mkfifo, "mkfifo", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_mknod, "mknod", "%s(\"%s\",%#o,%#x)", NULL, NULL },
{ TARGET_OPENBSD_NR_mlock, "mlock", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_mlockall, "mlockall", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_mmap, "mmap", NULL, NULL, print_syscall_ret_addr },
{ TARGET_OPENBSD_NR_mount, "mount", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_mprotect, "mprotect", "%s(%#x,%#x,%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_mquery, "mquery", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_msgctl, "msgctl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_msgget, "msgget", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_msgrcv, "msgrcv", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_msgsnd, "msgsnd", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_msync, "msync", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_munlock, "munlock", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_munlockall, "munlockall", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_munmap, "munmap", "%s(%p,%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_nanosleep, "nanosleep", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_nfssvc, "nfssvc", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_open, "open", "%s(\"%s\",%#x,%#o)", NULL, NULL },
{ TARGET_OPENBSD_NR_opipe, "opipe", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_osigaltstack, "osigaltstack", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_pathconf, "pathconf", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_pipe, "pipe", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_poll, "poll", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_pread, "pread", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_preadv, "preadv", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_profil, "profil", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_ptrace, "ptrace", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_pwrite, "pwrite", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_pwritev, "pwritev", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_quotactl, "quotactl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_read, "read", "%s(%d,%#x,%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_readlink, "readlink", "%s(\"%s\",%p,%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_readv, "readv", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_reboot, "reboot", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_recvfrom, "recvfrom", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_recvmsg, "recvmsg", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_rename, "rename", "%s(\"%s\",\"%s\")", NULL, NULL },
{ TARGET_OPENBSD_NR_revoke, "revoke", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_rfork, "rfork", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_rmdir, "rmdir", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sbrk, "sbrk", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sched_yield, "sched_yield", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_select, "select", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_semget, "semget", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_semop, "semop", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sendmsg, "sendmsg", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sendto, "sendto", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setegid, "setegid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_seteuid, "seteuid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setgid, "setgid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setgroups, "setgroups", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setitimer, "setitimer", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setlogin, "setlogin", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setpgid, "setpgid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setpriority, "setpriority", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setregid, "setregid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setresgid, "setresgid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setresuid, "setresuid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setreuid, "setreuid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setrlimit, "setrlimit", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setsid, "setsid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setsockopt, "setsockopt", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_settimeofday, "settimeofday", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_setuid, "setuid", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_shmat, "shmat", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_shmctl, "shmctl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_shmdt, "shmdt", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_shmget, "shmget", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_shutdown, "shutdown", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sigaction, "sigaction", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sigaltstack, "sigaltstack", "%s(%p,%p)", NULL, NULL },
{ TARGET_OPENBSD_NR_sigpending, "sigpending", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sigprocmask, "sigprocmask", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sigreturn, "sigreturn", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sigsuspend, "sigsuspend", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_socket, "socket", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_socketpair, "socketpair", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sstk, "sstk", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_stat, "stat", "%s(\"%s\",%p)", NULL, NULL },
{ TARGET_OPENBSD_NR_statfs, "statfs", "%s(\"%s\",%p)", NULL, NULL },
{ TARGET_OPENBSD_NR_swapctl, "swapctl", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_symlink, "symlink", "%s(\"%s\",\"%s\")", NULL, NULL },
{ TARGET_OPENBSD_NR_sync, "sync", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_sysarch, "sysarch", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_syscall, "syscall", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_threxit, "threxit", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_thrsigdivert, "thrsigdivert", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_thrsleep, "thrsleep", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_thrwakeup, "thrwakeup", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_truncate, "truncate", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_umask, "umask", "%s(%#o)", NULL, NULL },
{ TARGET_OPENBSD_NR_unlink, "unlink", "%s(\"%s\")", NULL, NULL },
{ TARGET_OPENBSD_NR_unmount, "unmount", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_utimes, "utimes", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_vfork, "vfork", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_wait4, "wait4", NULL, NULL, NULL },
{ TARGET_OPENBSD_NR_write, "write", "%s(%d,%#x,%d)", NULL, NULL },
{ TARGET_OPENBSD_NR_writev, "writev", "%s(%d,%p,%#x)", NULL, NULL },
{ TARGET_OPENBSD_NR_xfspioctl, "xfspioctl", NULL, NULL, NULL },
