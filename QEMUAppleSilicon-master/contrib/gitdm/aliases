#
# This is the email aliases file, mapping secondary addresses onto a
# single, canonical address. It duplicates some info from .mailmap so
# if you are adding something here also consider if the .mailmap needs
# updating.
#
# If you just want to avoid gitdm complaining about author fields
# which are actually email addresses with the message:
#
#   "...is an author name, probably not what you want"
#
# you can just apply --use-mailmap to you git-log command, e.g:
#
#   git log --use-mailmap --numstat --since "last 2 years" | $GITDM
#
# however that will have the effect of squashing multiple addresses to
# a canonical address which will distort the stats of those who
# contribute in both personal and professional capacities from
# different addresses.
#

# weird commits
balrog@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
aliguori@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
aurel32@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
blueswir1@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
edgar_igl@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
bellard@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
j_mayer@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
pbrook@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
ths@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>
malc@c046a42c-6fe2-441c-8c8c-71466251a162 <EMAIL>

# canonical emails
<EMAIL> <EMAIL>

# some broken DCO tags
yuval.shaia.ml.gmail.com <EMAIL>
jasowang <EMAIL>
nicta.com.au <EMAIL>

# There is also a:
#    (no author) <(no author)@c046a42c-6fe2-441c-8c8c-71466251a162>
# for the cvs2svn initialization commit e63c3dc74bf.

# Next, translate a few commits where mailman rewrote the From: line due
# to strict SPF, although we prefer to avoid adding more entries like that.
"Ed Swierk via Qemu-devel" <EMAIL>
"Ian McKellar via Qemu-devel" <EMAIL>
"Julia Suvorova via Qemu-devel" <EMAIL>
"Justin Terry (VM) via Qemu-devel" <EMAIL>
