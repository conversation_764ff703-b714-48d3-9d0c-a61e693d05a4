###########################################################################
#
# You can pass this file directly to qemu using the -readconfig
# command line switch.
#
# This config file creates a EHCI adapter with companion UHCI
# controllers as multifunction device in PCI slot "1d".
#
# Specify "bus=ehci.0" when creating usb devices to hook them up
# there.
#

[device "ehci"]
  driver = "ich9-usb-ehci1"
  addr = "1d.7"
  multifunction = "on"

[device "uhci-1"]
  driver = "ich9-usb-uhci1"
  addr = "1d.0"
  multifunction = "on"
  masterbus = "ehci.0"
  firstport = "0"

[device "uhci-2"]
  driver = "ich9-usb-uhci2"
  addr = "1d.1"
  multifunction = "on"
  masterbus = "ehci.0"
  firstport = "2"

[device "uhci-3"]
  driver = "ich9-usb-uhci3"
  addr = "1d.2"
  multifunction = "on"
  masterbus = "ehci.0"
  firstport = "4"
