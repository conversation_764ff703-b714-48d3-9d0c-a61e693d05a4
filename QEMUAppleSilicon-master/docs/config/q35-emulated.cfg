# q35 - Emulated guest (graphical console)
# =========================================================
#
# Usage:
#
#   $ qemu-system-x86_64 \
#     -nodefaults \
#     -readconfig q35-emulated.cfg
#
# You will probably need to tweak the lines marked as
# CHANGE ME before being able to use this configuration!
#
# The guest will have a selection of emulated devices that
# closely resembles that of a physical machine, and will be
# accessed through a graphical console.
#
# ---------------------------------------------------------
#
# Using -nodefaults is required to have full control over
# the virtual hardware: when it's specified, QEMU will
# populate the board with only the builtin peripherals
# plus a small selection of core PCI devices and
# controllers; the user will then have to explicitly add
# further devices.
#
# The core PCI devices show up in the guest as:
#
#   00:00.0 Host bridge
#   00:1f.0 ISA bridge / LPC
#   00:1f.2 SATA (AHCI) controller
#   00:1f.3 SMBus controller
#
# This configuration file adds a number of devices that
# are pretty much guaranteed to be present in every single
# physical machine based on q35, more specifically:
#
#   00:01.0 VGA compatible controller
#   00:19.0 Ethernet controller
#   00:1a.* USB controller (#2)
#   00:1b.0 Audio device
#   00:1c.* PCI bridge (PCI Express Root Ports)
#   00:1d.* USB Controller (#1)
#   00:1e.0 PCI bridge (legacy PCI bridge)
#
# More information about these devices is available below.


# Machine options
# =========================================================
#
# We use the q35 machine type and enable KVM acceleration
# for better performance.
#
# Using less than 1 GiB of memory is probably not going to
# yield good performance in the guest, and might even lead
# to obscure boot issues in some cases.
#
# Unfortunately, there is no way to configure the CPU model
# in this file, so it will have to be provided on the
# command line.

[machine]
  type = "q35"

[accel]
  accel = "kvm"

[memory]
  size = "1024"


# PCI bridge (PCI Express Root Ports)
# =========================================================
#
# We add four PCI Express Root Ports, all sharing the same
# slot on the PCI Express  Root Bus. These ports support
# hotplug.

[device "ich9-pcie-port-1"]
  driver = "ioh3420"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1c.0"
  port = "1"
  chassis = "1"

[device "ich9-pcie-port-2"]
  driver = "ioh3420"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1c.1"
  port = "2"
  chassis = "2"

[device "ich9-pcie-port-3"]
  driver = "ioh3420"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1c.2"
  port = "3"
  chassis = "3"

[device "ich9-pcie-port-4"]
  driver = "ioh3420"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1c.3"
  port = "4"
  chassis = "4"


# PCI bridge (legacy PCI bridge)
# =========================================================
#
# This bridge can be used to build an independent topology
# for legacy PCI devices. PCI Express devices should be
# plugged into PCI Express slots instead, so ideally there
# will be no devices connected to this bridge.

[device "ich9-pci-bridge"]
  driver = "i82801b11-bridge"
  bus = "pcie.0"
  addr = "1e.0"


# SATA storage
# =========================================================
#
# An implicit SATA controller is created automatically for
# every single q35 guest; here we create a disk, backed by
# a qcow2 disk image on the host's filesystem, and attach
# it to that controller so that the guest can use it.
#
# We also create an optical disk, mostly for installation
# purposes: once the guest OS has been successfully
# installed, the guest will no longer boot from optical
# media. If you don't want, or no longer want, to have an
# optical disk in the guest you can safely comment out
# all relevant sections below.

[device "sata-disk"]
  driver = "ide-hd"
  bus = "ide.0"
  drive = "disk"
  bootindex = "1"

[drive "disk"]
  file = "guest.qcow2"                          # CHANGE ME
  format = "qcow2"
  if = "none"

[device "sata-optical-disk"]
  driver = "ide-cd"
  bus = "ide.1"
  drive = "optical-disk"
  bootindex = "2"

[drive "optical-disk"]
  file = "install.iso"                          # CHANGE ME
  format = "raw"
  if = "none"


# USB controller (#1)
# =========================================================
#
# EHCI controller + UHCI companion controllers.

[device "ich9-ehci-1"]
  driver = "ich9-usb-ehci1"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1d.7"

[device "ich9-uhci-1"]
  driver = "ich9-usb-uhci1"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1d.0"
  masterbus = "ich9-ehci-1.0"
  firstport = "0"

[device "ich9-uhci-2"]
  driver = "ich9-usb-uhci2"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1d.1"
  masterbus = "ich9-ehci-1.0"
  firstport = "2"

[device "ich9-uhci-3"]
  driver = "ich9-usb-uhci3"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1d.2"
  masterbus = "ich9-ehci-1.0"
  firstport = "4"


# USB controller (#2)
# =========================================================
#
# EHCI controller + UHCI companion controllers.

[device "ich9-ehci-2"]
  driver = "ich9-usb-ehci2"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1a.7"

[device "ich9-uhci-4"]
  driver = "ich9-usb-uhci4"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1a.0"
  masterbus = "ich9-ehci-2.0"
  firstport = "0"

[device "ich9-uhci-5"]
  driver = "ich9-usb-uhci5"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1a.1"
  masterbus = "ich9-ehci-2.0"
  firstport = "2"

[device "ich9-uhci-6"]
  driver = "ich9-usb-uhci6"
  multifunction = "on"
  bus = "pcie.0"
  addr = "1a.2"
  masterbus = "ich9-ehci-2.0"
  firstport = "4"


# Ethernet controller
# =========================================================
#
# We add a Gigabit Ethernet interface to the guest; on the
# host side, we take advantage of user networking so that
# the QEMU process doesn't require any additional
# privileges.

[netdev "hostnet"]
  type = "user"

[device "net"]
  driver = "e1000"
  netdev = "hostnet"
  bus = "pcie.0"
  addr = "19.0"


# VGA compatible controller
# =========================================================
#
# We use stdvga instead of Cirrus as it supports more video
# modes and is closer to what actual hardware looks like.
#
# If you're running the guest on a remote, potentially
# headless host, you will probably want to append something
# like
#
#   -display vnc=127.0.0.1:0
#
# to the command line in order to prevent QEMU from
# creating a graphical display window on the host and
# enable remote access instead.

[device "video"]
  driver = "VGA"
  bus = "pcie.0"
  addr = "01.0"


# Audio device
# =========================================================
#
# The sound card is a legacy PCI device that is plugged
# directly into the PCI Express Root Bus.

[device "ich9-hda-audio"]
  driver = "ich9-intel-hda"
  bus = "pcie.0"
  addr = "1b.0"

[device "ich9-hda-duplex"]
  driver = "hda-duplex"
  bus = "ich9-hda-audio.0"
  cad = "0"
  audiodev = "audiodev0"

[audiodev "audiodev0"]
  driver = "none"                      # CHANGE ME
