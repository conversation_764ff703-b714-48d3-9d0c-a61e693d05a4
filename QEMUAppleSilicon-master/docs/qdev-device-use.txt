= How to convert to -device & friends =

=== Specifying Bus and Address on Bus ===

In qdev, each device has a parent bus.  Some devices provide one or
more buses for children.  You can specify a device's parent bus with
-device parameter bus.

A device typically has a device address on its parent bus.  For buses
where this address can be configured, devices provide a bus-specific
property.  Examples:

    bus         property name       value format
    PCI         addr                %x.%x    (dev.fn, .fn optional)
    I2C         address             %u
    SCSI        scsi-id             %u
    IDE         unit                %u
    HDA         cad                 %u
    virtio-serial-bus  nr           %u
    ccid-bus    slot                %u
    USB         port                %d(.%d)*    (port.port...)

Example: device i440FX-pcihost is on the root bus, and provides a PCI
bus named pci.0.  To put a FOO device into its slot 4, use -device
FOO,bus=/i440FX-pcihost/pci.0,addr=4.  The abbreviated form bus=pci.0
also works as long as the bus name is unique.

=== Block Devices ===

A QEMU block device (drive) has a host and a guest part.

In the general case, the guest device is connected to a controller
device.  For instance, the IDE controller provides two IDE buses, each
of which can have up to two devices, and each device is a guest part,
and is connected to a host part.

Except we sometimes lump controller, bus(es) and drive device(s) all
together into a single device.  For instance, the ISA floppy
controller is connected to up to two host drives.

The old ways to define block devices define host and guest part
together.  Sometimes, they can even define a controller device in
addition to the block device.

The new way keeps the parts separate: you create the host part with
-drive, and guest device(s) with -device.

The various old ways to define drives all boil down to the common form

    -drive if=TYPE,bus=BUS,unit=UNIT,OPTS...

TYPE, BUS and UNIT identify the controller device, which of its buses
to use, and the drive's address on that bus.  Details depend on TYPE.

Instead of bus=BUS,unit=UNIT, you can also say index=IDX.

In the new way, this becomes something like

   -drive if=none,id=DRIVE-ID,HOST-OPTS...
   -device DEVNAME,drive=DRIVE-ID,DEV-OPTS...

The old OPTS get split into HOST-OPTS and DEV-OPTS as follows:

* file, format, snapshot, cache, aio, readonly, rerror, werror go into
  HOST-OPTS.

* cyls, head, secs and trans go into HOST-OPTS.  Future work: they
  should go into DEV-OPTS instead.

* serial goes into DEV-OPTS, for devices supporting serial numbers.
  For other devices, it goes nowhere.

* media is special.  In the old way, it selects disk vs. CD-ROM with
  if=ide, if=scsi and if=xen.  The new way uses DEVNAME for that.
  Additionally, readonly=on goes into HOST-OPTS.

* addr is special, see if=virtio below.

The -device argument differs in detail for each type of drive:

* if=ide

  -device DEVNAME,drive=DRIVE-ID,bus=IDE-BUS,unit=UNIT

  where DEVNAME is either ide-hd or ide-cd, IDE-BUS identifies an IDE
  bus, normally either ide.0 or ide.1, and UNIT is either 0 or 1.

* if=scsi

  The old way implicitly creates SCSI controllers as needed.  The new
  way makes that explicit:

  -device lsi53c895a,id=ID

  As for all PCI devices, you can add bus=PCI-BUS,addr=DEVFN to
  control the PCI device address.

  This SCSI controller provides a single SCSI bus, named ID.0.  Put a
  disk on it:

  -device DEVNAME,drive=DRIVE-ID,bus=ID.0,scsi-id=UNIT

  where DEVNAME is either scsi-hd, scsi-cd or scsi-generic.

* if=floppy

  -device floppy,unit=UNIT,drive=DRIVE-ID

  Without any -device floppy,... you get an empty unit 0 and no unit
  1.  You can use -nodefaults to suppress the default unit 0, see
  "Default Devices".

* if=virtio

  -device virtio-blk-pci,drive=DRIVE-ID,class=C,vectors=V,ioeventfd=IOEVENTFD

  This lets you control PCI device class and MSI-X vectors.

  IOEVENTFD controls whether or not ioeventfd is used for virtqueue
  notify.  It can be set to on (default) or off.

  As for all PCI devices, you can add bus=PCI-BUS,addr=DEVFN to
  control the PCI device address.  This replaces option addr available
  with -drive if=virtio.

* if=pflash, if=mtd, if=sd, if=xen are not yet available with -device

For USB devices, the old way was actually different:

    -usbdevice disk:format=FMT:FILENAME

"Was" because "disk:" is gone since v2.12.0.

The old way provided much less control than -drive's OPTS...  The new
way fixes that:

    -device usb-storage,drive=DRIVE-ID,removable=RMB

The removable parameter gives control over the SCSI INQUIRY removable
(RMB) bit.  USB thumbdrives usually set removable=on, while USB hard
disks set removable=off.

Bug: usb-storage pretends to be a block device, but it's really a SCSI
controller that can serve only a single device, which it creates
automatically.  The automatic creation guesses what kind of guest part
to create from the host part, like -drive if=scsi.  Host and guest
part are not cleanly separated.

=== Character Devices ===

A QEMU character device has a host and a guest part.

The old ways to define character devices define host and guest part
together.

The new way keeps the parts separate: you create the host part with
-chardev, and the guest device with -device.

The various old ways to define a character device are all of the
general form

    -FOO FOO-OPTS...,LEGACY-CHARDEV

where FOO-OPTS... is specific to -FOO, and the host part
LEGACY-CHARDEV is the same everywhere.

In the new way, this becomes

    -chardev HOST-OPTS...,id=CHR-ID
    -device DEVNAME,chardev=CHR-ID,DEV-OPTS...

The appropriate DEVNAME depends on the machine type.  For type "pc":

* -serial becomes -device isa-serial,iobase=IOADDR,irq=IRQ,index=IDX

  This lets you control I/O ports and IRQs.

* -parallel becomes -device isa-parallel,iobase=IOADDR,irq=IRQ,index=IDX

  This lets you control I/O ports and IRQs.

* -usbdevice braille doesn't support LEGACY-CHARDEV syntax.  It always
  uses "braille".  With -device, this useful default is gone, so you
  have to use something like

  -device usb-braille,chardev=braille -chardev braille,id=braille

* -usbdevice serial::chardev is gone since v2.12.0.  It became
  -device usb-serial,chardev=dev.

LEGACY-CHARDEV translates to -chardev HOST-OPTS... as follows:

* null becomes -chardev null

* pty, msmouse, wctablet, braille, stdio likewise

* vc:WIDTHxHEIGHT becomes -chardev vc,width=WIDTH,height=HEIGHT

* vc:<COLS>Cx<ROWS>C becomes -chardev vc,cols=<COLS>,rows=<ROWS>

* con: becomes -chardev console

* COM<NUM> becomes -chardev serial,path=COM<NUM>

* file:FNAME becomes -chardev file,path=FNAME

* pipe:FNAME becomes -chardev pipe,path=FNAME

* tcp:HOST:PORT,OPTS... becomes -chardev socket,host=HOST,port=PORT,OPTS...

* telnet:HOST:PORT,OPTS... becomes
  -chardev socket,host=HOST,port=PORT,OPTS...,telnet=on

* udp:HOST:PORT@LOCALADDR:LOCALPORT becomes
  -chardev udp,host=HOST,port=PORT,localaddr=LOCALADDR,localport=LOCALPORT

* unix:FNAME becomes -chardev socket,path=FNAME

* /dev/parportN becomes -chardev parallel,file=/dev/parportN

* /dev/ppiN likewise

* Any other /dev/FNAME becomes -chardev serial,path=/dev/FNAME

* mon:LEGACY-CHARDEV is special: it multiplexes the monitor onto the
  character device defined by LEGACY-CHARDEV.  -chardev provides more
  general multiplexing instead: you can connect up to four users to a
  single host part.  You need to pass mux=on to -chardev to enable
  switching the input focus.

QEMU uses LEGACY-CHARDEV syntax not just to set up guest devices, but
also in various other places such as -monitor or -net
user,guestfwd=...  You can use chardev:CHR-ID in place of
LEGACY-CHARDEV to refer to a host part defined with -chardev.

=== Network Devices ===

Host and guest part of network devices have always been separate.

The old way to define the guest part looks like this:

    -net nic,netdev=NET-ID,macaddr=MACADDR,model=MODEL,name=ID,addr=STR,vectors=V

Except for USB it looked like this:

    -usbdevice net:netdev=NET-ID,macaddr=MACADDR,name=ID

"Looked" because "net:" is gone since v2.12.0.

The new way is -device:

    -device DEVNAME,netdev=NET-ID,mac=MACADDR,DEV-OPTS...

DEVNAME equals MODEL, except for virtio you have to name the virtio
device appropriate for the bus (virtio-net-pci for PCI), and for USB
you have to use usb-net.

The old name=ID parameter becomes the usual id=ID with -device.

For PCI devices, you can add bus=PCI-BUS,addr=DEVFN to control the PCI
device address, as usual.  The old -net nic provides parameter addr
for that, which is silently ignored when the NIC is not a PCI device.

For virtio-net-pci, you can control whether or not ioeventfd is used for
virtqueue notify by setting ioeventfd= to on or off (default).

-net nic accepts vectors=V for all models, but it's silently ignored
except for virtio-net-pci (model=virtio).  With -device, only devices
that support it accept it.

Not all devices are available with -device at this time.  All PCI
devices and ne2k_isa are.

Some PCI devices aren't available with -net nic, e.g. i82558a.

=== Graphics Devices ===

Host and guest part of graphics devices have always been separate.

The old way to define the guest graphics device is -vga VGA.  Not all
machines support all -vga options.

The new way is -device.  The mapping from -vga argument to -device
depends on the machine type.  For machine "pc", it's:

    std         -device VGA
    cirrus      -device cirrus-vga
    vmware      -device vmware-svga
    qxl         -device qxl-vga
    none        -nodefaults
                disables more than just VGA, see "Default Devices"

As for all PCI devices, you can add bus=PCI-BUS,addr=DEVFN to control
the PCI device address.

-device VGA supports properties bios-offset and bios-size, but they
aren't used with machine type "pc".

For machine "isapc", it's

    std         -device isa-vga
    cirrus      not yet available with -device
    none        -nodefaults
                disables more than just VGA, see "Default Devices"

Bug: the new way doesn't work for machine types "pc" and "isapc",
because it violates obscure device initialization ordering
constraints.

=== Audio Devices ===

Host and guest part of audio devices have always been separate.

The old way to define guest audio devices is -soundhw C1,...

The new way is to define each guest audio device separately with
-device.

Map from -soundhw sound card name to -device:

    ac97        -device AC97
    cs4231a     -device cs4231a,iobase=IOADDR,irq=IRQ,dma=DMA
    es1370      -device ES1370
    gus         -device gus,iobase=IOADDR,irq=IRQ,dma=DMA,freq=F
    hda         -device intel-hda,msi=MSI -device hda-duplex
    sb16        -device sb16,iobase=IOADDR,irq=IRQ,dma=DMA,dma16=DMA16,version=V
    adlib       not yet available with -device
    pcspk       not yet available with -device

For PCI devices, you can add bus=PCI-BUS,addr=DEVFN to control the PCI
device address, as usual.

=== USB Devices ===

The old way to define a virtual USB device is -usbdevice DRIVER:OPTS...

The new way is -device DEVNAME,DEV-OPTS...  Details depend on DRIVER:

* ccid            -device usb-ccid
* keyboard        -device usb-kbd
* mouse           -device usb-mouse
* tablet          -device usb-tablet
* wacom-tablet    -device usb-wacom-tablet
* u2f             -device u2f-{emulated,passthru}
* braille         See "Character Devices"

Until v2.12.0, we additionally had

* host:...        See "Host Device Assignment"
* disk:...        See "Block Devices"
* serial:...      See "Character Devices"
* net:...         See "Network Devices"

=== Watchdog Devices ===

Host and guest part of watchdog devices have always been separate.

The old way to define a guest watchdog device is -watchdog DEVNAME.
The new way is -device DEVNAME.  For PCI devices, you can add
bus=PCI-BUS,addr=DEVFN to control the PCI device address, as usual.

=== Host Device Assignment ===

QEMU supports assigning host PCI devices (qemu-kvm only at this time)
and host USB devices.  PCI devices can only be assigned with -device:

    -device vfio-pci,host=ADDR,id=ID

The old way to assign a USB host device

    -usbdevice host:auto:BUS.ADDR:VID:PRID

was removed in v2.12.0.  Any of BUS, ADDR, VID, PRID could be the
wildcard *.

The new way is

    -device usb-host,hostbus=BUS,hostaddr=ADDR,vendorid=VID,productid=PRID

Omitted options match anything.

=== Default Devices ===

QEMU creates a number of devices by default, depending on the machine
type.

-device DEVNAME... and global DEVNAME... suppress default devices for
some DEVNAMEs:

    default device      suppressing DEVNAMEs
    CD-ROM              ide-cd, ide-hd, scsi-cd, scsi-hd
    floppy              floppy, isa-fdc
    parallel            isa-parallel
    serial              isa-serial
    VGA                 VGA, cirrus-vga, isa-vga, isa-cirrus-vga,
                        vmware-svga, qxl-vga, virtio-vga, ati-vga,
                        vhost-user-vga

The default NIC is connected to a default part created along with it.
It is *not* suppressed by configuring a NIC with -device (you may call
that a bug).  -net and -netdev suppress the default NIC.

-nodefaults suppresses all the default devices mentioned above, plus a
few other things such as default SD-Card drive and default monitor.
