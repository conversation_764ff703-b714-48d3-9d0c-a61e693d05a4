<?xml version="1.0"?>
<!-- Copyright (C) 2018-2019 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!-- Register numbers are hard-coded in order to maintain backward
     compatibility with older versions of tools that didn't use xml
     register descriptions.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.riscv.cpu">
  <reg name="r0" bitsize="8" type="int" regnum="0"/>
  <reg name="r1" bitsize="8" type="int"/>
  <reg name="r2" bitsize="8" type="int"/>
  <reg name="r3" bitsize="8" type="int"/>
  <reg name="r4" bitsize="8" type="int"/>
  <reg name="r5" bitsize="8" type="int"/>
  <reg name="r6" bitsize="8" type="int"/>
  <reg name="r7" bitsize="8" type="int"/>
  <reg name="r8" bitsize="8" type="int"/>
  <reg name="r9" bitsize="8" type="int"/>
  <reg name="r10" bitsize="8" type="int"/>
  <reg name="r11" bitsize="8" type="int"/>
  <reg name="r12" bitsize="8" type="int"/>
  <reg name="r13" bitsize="8" type="int"/>
  <reg name="r14" bitsize="8" type="int"/>
  <reg name="r15" bitsize="8" type="int"/>
  <reg name="r16" bitsize="8" type="int"/>
  <reg name="r17" bitsize="8" type="int"/>
  <reg name="r18" bitsize="8" type="int"/>
  <reg name="r19" bitsize="8" type="int"/>
  <reg name="r20" bitsize="8" type="int"/>
  <reg name="r21" bitsize="8" type="int"/>
  <reg name="r22" bitsize="8" type="int"/>
  <reg name="r23" bitsize="8" type="int"/>
  <reg name="r24" bitsize="8" type="int"/>
  <reg name="r25" bitsize="8" type="int"/>
  <reg name="r26" bitsize="8" type="int"/>
  <reg name="r27" bitsize="8" type="int"/>
  <reg name="r28" bitsize="8" type="int"/>
  <reg name="r29" bitsize="8" type="int"/>
  <reg name="r30" bitsize="8" type="int"/>
  <reg name="r31" bitsize="8" type="int"/>
  <reg name="sreg" bitsize="8" type="int"/>
  <reg name="sp" bitsize="8" type="int"/>
  <reg name="pc" bitsize="8" type="int"/>
</feature>
