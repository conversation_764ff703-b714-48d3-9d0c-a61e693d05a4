<?xml version="1.0"?>
<!--
  Copyright(c) 2023-2024 Qualcomm Innovation Center, Inc. All Rights Reserved.

  This work is licensed under the terms of the GNU GPL, version 2 or
  (at your option) any later version. See the COPYING file in the
  top-level directory.

  Note: this file is intended to be use with LLDB, so it contains fields
  that may be unknown to GDB. For more information on such fields, please
  see:
  https://github.com/llvm/llvm-project/blob/287aa6c4536408413b860e61fca0318a27214cf3/lldb/docs/lldb-gdb-remote.txt#L738-L860
  https://github.com/llvm/llvm-project/blob/287aa6c4536408413b860e61fca0318a27214cf3/lldb/source/Plugins/Process/gdb-remote/ProcessGDBRemote.cpp#L4275-L4335
-->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.hexagon.core">

  <reg name="r00" altname="r0" bitsize="32" offset="0"   encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="0" generic="r00"/>
  <reg name="r01" altname="r1" bitsize="32" offset="4"   encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="1" generic="r01"/>
  <reg name="r02" altname="r2" bitsize="32" offset="8"   encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="2" generic="r02"/>
  <reg name="r03" altname="r3" bitsize="32" offset="12"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="3" generic="r03"/>
  <reg name="r04" altname="r4" bitsize="32" offset="16"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="4" generic="r04"/>
  <reg name="r05" altname="r5" bitsize="32" offset="20"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="5" generic="r05"/>
  <reg name="r06" altname="r6" bitsize="32" offset="24"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="6" generic="r06"/>
  <reg name="r07" altname="r7" bitsize="32" offset="28"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="7" generic="r07"/>
  <reg name="r08" altname="r8" bitsize="32" offset="32"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="8" generic="r08"/>
  <reg name="r09" altname="r9" bitsize="32" offset="36"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="9" generic="r09"/>
  <reg name="r10"              bitsize="32" offset="40"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="10"/>
  <reg name="r11"              bitsize="32" offset="44"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="11"/>
  <reg name="r12"              bitsize="32" offset="48"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="12"/>
  <reg name="r13"              bitsize="32" offset="52"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="13"/>
  <reg name="r14"              bitsize="32" offset="56"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="14"/>
  <reg name="r15"              bitsize="32" offset="60"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="15"/>
  <reg name="r16"              bitsize="32" offset="64"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="16"/>
  <reg name="r17"              bitsize="32" offset="68"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="17"/>
  <reg name="r18"              bitsize="32" offset="72"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="18"/>
  <reg name="r19"              bitsize="32" offset="76"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="19"/>
  <reg name="r20"              bitsize="32" offset="80"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="20"/>
  <reg name="r21"              bitsize="32" offset="84"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="21"/>
  <reg name="r22"              bitsize="32" offset="88"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="22"/>
  <reg name="r23"              bitsize="32" offset="92"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="23"/>
  <reg name="r24"              bitsize="32" offset="96"  encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="24"/>
  <reg name="r25"              bitsize="32" offset="100" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="25"/>
  <reg name="r26"              bitsize="32" offset="104" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="26"/>
  <reg name="r27"              bitsize="32" offset="108" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="27"/>
  <reg name="r28"              bitsize="32" offset="112" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="28"/>
  <reg name="r29" altname="sp" bitsize="32" offset="116" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="29" generic="sp"/>
  <reg name="r30" altname="fp" bitsize="32" offset="120" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="30" generic="fp"/>
  <reg name="r31" altname="ra" bitsize="32" offset="124" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="31" generic="ra"/>
  <reg name="sa0"              bitsize="32" offset="128" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="32"/>
  <reg name="lc0"              bitsize="32" offset="132" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="33"/>
  <reg name="sa1"              bitsize="32" offset="136" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="34"/>
  <reg name="lc1"              bitsize="32" offset="140" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="35"/>
  <reg name="p3_0"             bitsize="32" offset="144" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="36"/>
  <reg name="c5"               bitsize="32" offset="148" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="37"/>
  <reg name="m0"               bitsize="32" offset="152" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="38"/>
  <reg name="m1"               bitsize="32" offset="156" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="39"/>
  <reg name="usr"              bitsize="32" offset="160" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="40"/>
  <reg name="pc"               bitsize="32" offset="164" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="41" generic="pc"/>
  <reg name="ugp"              bitsize="32" offset="168" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="42"/>
  <reg name="gp"               bitsize="32" offset="172" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="43"/>
  <reg name="cs0"              bitsize="32" offset="176" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="44"/>
  <reg name="cs1"              bitsize="32" offset="180" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="45"/>
  <reg name="upcyclelo"        bitsize="32" offset="184" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="46"/>
  <reg name="upcyclehi"        bitsize="32" offset="188" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="47"/>
  <reg name="framelimit"       bitsize="32" offset="192" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="48"/>
  <reg name="framekey"         bitsize="32" offset="196" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="49"/>
  <reg name="pktcountlo"       bitsize="32" offset="200" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="50"/>
  <reg name="pktcounthi"       bitsize="32" offset="204" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="51"/>
  <reg name="pkt_cnt"          bitsize="32" offset="208" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="52"/>
  <reg name="insn_cnt"         bitsize="32" offset="212" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="53"/>
  <reg name="hvx_cnt"          bitsize="32" offset="216" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="54"/>
  <reg name="c23"              bitsize="32" offset="220" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="55"/>
  <reg name="c24"              bitsize="32" offset="224" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="56"/>
  <reg name="c25"              bitsize="32" offset="228" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="57"/>
  <reg name="c26"              bitsize="32" offset="232" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="58"/>
  <reg name="c27"              bitsize="32" offset="236" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="59"/>
  <reg name="c28"              bitsize="32" offset="240" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="60"/>
  <reg name="c29"              bitsize="32" offset="244" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="61"/>
  <reg name="utimerlo"         bitsize="32" offset="248" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="62"/>
  <reg name="utimerhi"         bitsize="32" offset="252" encoding="uint" format="hex" group="Thread Registers" dwarf_regnum="63"/>
  <reg name="p0"               bitsize="8"  offset="256" encoding="uint" format="hex" group="Predicate Registers" dwarf_regnum="64"/>
  <reg name="p1"               bitsize="8"  offset="257" encoding="uint" format="hex" group="Predicate Registers" dwarf_regnum="65"/>
  <reg name="p2"               bitsize="8"  offset="258" encoding="uint" format="hex" group="Predicate Registers" dwarf_regnum="66"/>
  <reg name="p3"               bitsize="8"  offset="259" encoding="uint" format="hex" group="Predicate Registers" dwarf_regnum="67"/>

</feature>
