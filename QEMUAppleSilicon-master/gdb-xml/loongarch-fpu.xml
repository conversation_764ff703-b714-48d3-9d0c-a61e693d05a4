<?xml version="1.0"?>
<!-- Copyright (C) 2021 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.loongarch.fpu">

  <union id="fputype">
    <field name="f" type="ieee_single"/>
    <field name="d" type="ieee_double"/>
  </union>

  <reg name="f0" bitsize="64" type="fputype" group="float"/>
  <reg name="f1" bitsize="64" type="fputype" group="float"/>
  <reg name="f2" bitsize="64" type="fputype" group="float"/>
  <reg name="f3" bitsize="64" type="fputype" group="float"/>
  <reg name="f4" bitsize="64" type="fputype" group="float"/>
  <reg name="f5" bitsize="64" type="fputype" group="float"/>
  <reg name="f6" bitsize="64" type="fputype" group="float"/>
  <reg name="f7" bitsize="64" type="fputype" group="float"/>
  <reg name="f8" bitsize="64" type="fputype" group="float"/>
  <reg name="f9" bitsize="64" type="fputype" group="float"/>
  <reg name="f10" bitsize="64" type="fputype" group="float"/>
  <reg name="f11" bitsize="64" type="fputype" group="float"/>
  <reg name="f12" bitsize="64" type="fputype" group="float"/>
  <reg name="f13" bitsize="64" type="fputype" group="float"/>
  <reg name="f14" bitsize="64" type="fputype" group="float"/>
  <reg name="f15" bitsize="64" type="fputype" group="float"/>
  <reg name="f16" bitsize="64" type="fputype" group="float"/>
  <reg name="f17" bitsize="64" type="fputype" group="float"/>
  <reg name="f18" bitsize="64" type="fputype" group="float"/>
  <reg name="f19" bitsize="64" type="fputype" group="float"/>
  <reg name="f20" bitsize="64" type="fputype" group="float"/>
  <reg name="f21" bitsize="64" type="fputype" group="float"/>
  <reg name="f22" bitsize="64" type="fputype" group="float"/>
  <reg name="f23" bitsize="64" type="fputype" group="float"/>
  <reg name="f24" bitsize="64" type="fputype" group="float"/>
  <reg name="f25" bitsize="64" type="fputype" group="float"/>
  <reg name="f26" bitsize="64" type="fputype" group="float"/>
  <reg name="f27" bitsize="64" type="fputype" group="float"/>
  <reg name="f28" bitsize="64" type="fputype" group="float"/>
  <reg name="f29" bitsize="64" type="fputype" group="float"/>
  <reg name="f30" bitsize="64" type="fputype" group="float"/>
  <reg name="f31" bitsize="64" type="fputype" group="float"/>
  <reg name="fcc0" bitsize="8" type="uint8" group="float"/>
  <reg name="fcc1" bitsize="8" type="uint8" group="float"/>
  <reg name="fcc2" bitsize="8" type="uint8" group="float"/>
  <reg name="fcc3" bitsize="8" type="uint8" group="float"/>
  <reg name="fcc4" bitsize="8" type="uint8" group="float"/>
  <reg name="fcc5" bitsize="8" type="uint8" group="float"/>
  <reg name="fcc6" bitsize="8" type="uint8" group="float"/>
  <reg name="fcc7" bitsize="8" type="uint8" group="float"/>
  <reg name="fcsr" bitsize="32" type="uint32" group="float"/>
</feature>
