<?xml version="1.0"?>
<!-- Copyright (C) 2008 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.microblaze.core">
  <reg name="r0" bitsize="32" regnum="0"/>
  <reg name="r1" bitsize="32" type="data_ptr"/>
  <reg name="r2" bitsize="32"/>
  <reg name="r3" bitsize="32"/>
  <reg name="r4" bitsize="32"/>
  <reg name="r5" bitsize="32"/>
  <reg name="r6" bitsize="32"/>
  <reg name="r7" bitsize="32"/>
  <reg name="r8" bitsize="32"/>
  <reg name="r9" bitsize="32"/>
  <reg name="r10" bitsize="32"/>
  <reg name="r11" bitsize="32"/>
  <reg name="r12" bitsize="32"/>
  <reg name="r13" bitsize="32"/>
  <reg name="r14" bitsize="32"/>
  <reg name="r15" bitsize="32"/>
  <reg name="r16" bitsize="32"/>
  <reg name="r17" bitsize="32"/>
  <reg name="r18" bitsize="32"/>
  <reg name="r19" bitsize="32"/>
  <reg name="r20" bitsize="32"/>
  <reg name="r21" bitsize="32"/>
  <reg name="r22" bitsize="32"/>
  <reg name="r23" bitsize="32"/>
  <reg name="r24" bitsize="32"/>
  <reg name="r25" bitsize="32"/>
  <reg name="r26" bitsize="32"/>
  <reg name="r27" bitsize="32"/>
  <reg name="r28" bitsize="32"/>
  <reg name="r29" bitsize="32"/>
  <reg name="r30" bitsize="32"/>
  <reg name="r31" bitsize="32"/>
  <reg name="rpc" bitsize="32" type="code_ptr"/>
  <reg name="rmsr" bitsize="32"/>
  <reg name="rear" bitsize="32"/>
  <reg name="resr" bitsize="32"/>
  <reg name="rfsr" bitsize="32"/>
  <reg name="rbtr" bitsize="32"/>
  <reg name="rpvr0" bitsize="32"/>
  <reg name="rpvr1" bitsize="32"/>
  <reg name="rpvr2" bitsize="32"/>
  <reg name="rpvr3" bitsize="32"/>
  <reg name="rpvr4" bitsize="32"/>
  <reg name="rpvr5" bitsize="32"/>
  <reg name="rpvr6" bitsize="32"/>
  <reg name="rpvr7" bitsize="32"/>
  <reg name="rpvr8" bitsize="32"/>
  <reg name="rpvr9" bitsize="32"/>
  <reg name="rpvr10" bitsize="32"/>
  <reg name="rpvr11" bitsize="32"/>
  <reg name="redr" bitsize="32"/>
  <reg name="rpid" bitsize="32"/>
  <reg name="rzpr" bitsize="32"/>
  <reg name="rtlbx" bitsize="32"/>
  <reg name="rtlbsx" bitsize="32"/>
  <reg name="rtlblo" bitsize="32"/>
  <reg name="rtlbhi" bitsize="32"/>
</feature>
