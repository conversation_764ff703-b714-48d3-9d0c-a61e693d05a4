<?xml version="1.0"?>
<!-- Copyright (C) 2008-2015 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!-- POWER7 VSX registers that do not overlap existing FP and VMX
     registers.  -->
<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.power.vsx">
  <reg name="vs0h" bitsize="64" type="uint64"/>
  <reg name="vs1h" bitsize="64" type="uint64"/>
  <reg name="vs2h" bitsize="64" type="uint64"/>
  <reg name="vs3h" bitsize="64" type="uint64"/>
  <reg name="vs4h" bitsize="64" type="uint64"/>
  <reg name="vs5h" bitsize="64" type="uint64"/>
  <reg name="vs6h" bitsize="64" type="uint64"/>
  <reg name="vs7h" bitsize="64" type="uint64"/>
  <reg name="vs8h" bitsize="64" type="uint64"/>
  <reg name="vs9h" bitsize="64" type="uint64"/>
  <reg name="vs10h" bitsize="64" type="uint64"/>
  <reg name="vs11h" bitsize="64" type="uint64"/>
  <reg name="vs12h" bitsize="64" type="uint64"/>
  <reg name="vs13h" bitsize="64" type="uint64"/>
  <reg name="vs14h" bitsize="64" type="uint64"/>
  <reg name="vs15h" bitsize="64" type="uint64"/>
  <reg name="vs16h" bitsize="64" type="uint64"/>
  <reg name="vs17h" bitsize="64" type="uint64"/>
  <reg name="vs18h" bitsize="64" type="uint64"/>
  <reg name="vs19h" bitsize="64" type="uint64"/>
  <reg name="vs20h" bitsize="64" type="uint64"/>
  <reg name="vs21h" bitsize="64" type="uint64"/>
  <reg name="vs22h" bitsize="64" type="uint64"/>
  <reg name="vs23h" bitsize="64" type="uint64"/>
  <reg name="vs24h" bitsize="64" type="uint64"/>
  <reg name="vs25h" bitsize="64" type="uint64"/>
  <reg name="vs26h" bitsize="64" type="uint64"/>
  <reg name="vs27h" bitsize="64" type="uint64"/>
  <reg name="vs28h" bitsize="64" type="uint64"/>
  <reg name="vs29h" bitsize="64" type="uint64"/>
  <reg name="vs30h" bitsize="64" type="uint64"/>
  <reg name="vs31h" bitsize="64" type="uint64"/>
</feature>
