<?xml version="1.0"?>
<!-- Copyright (C) 2018-2019 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.riscv.cpu">
  <reg name="zero" bitsize="64" type="int"/>
  <reg name="ra" bitsize="64" type="code_ptr"/>
  <reg name="sp" bitsize="64" type="data_ptr"/>
  <reg name="gp" bitsize="64" type="data_ptr"/>
  <reg name="tp" bitsize="64" type="data_ptr"/>
  <reg name="t0" bitsize="64" type="int"/>
  <reg name="t1" bitsize="64" type="int"/>
  <reg name="t2" bitsize="64" type="int"/>
  <reg name="fp" bitsize="64" type="data_ptr"/>
  <reg name="s1" bitsize="64" type="int"/>
  <reg name="a0" bitsize="64" type="int"/>
  <reg name="a1" bitsize="64" type="int"/>
  <reg name="a2" bitsize="64" type="int"/>
  <reg name="a3" bitsize="64" type="int"/>
  <reg name="a4" bitsize="64" type="int"/>
  <reg name="a5" bitsize="64" type="int"/>
  <reg name="a6" bitsize="64" type="int"/>
  <reg name="a7" bitsize="64" type="int"/>
  <reg name="s2" bitsize="64" type="int"/>
  <reg name="s3" bitsize="64" type="int"/>
  <reg name="s4" bitsize="64" type="int"/>
  <reg name="s5" bitsize="64" type="int"/>
  <reg name="s6" bitsize="64" type="int"/>
  <reg name="s7" bitsize="64" type="int"/>
  <reg name="s8" bitsize="64" type="int"/>
  <reg name="s9" bitsize="64" type="int"/>
  <reg name="s10" bitsize="64" type="int"/>
  <reg name="s11" bitsize="64" type="int"/>
  <reg name="t3" bitsize="64" type="int"/>
  <reg name="t4" bitsize="64" type="int"/>
  <reg name="t5" bitsize="64" type="int"/>
  <reg name="t6" bitsize="64" type="int"/>
  <reg name="pc" bitsize="64" type="code_ptr"/>
</feature>
