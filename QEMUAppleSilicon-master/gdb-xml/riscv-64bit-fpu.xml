<?xml version="1.0"?>
<!-- Copyright (C) 2018-2019 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.riscv.fpu">

  <union id="riscv_double">
    <field name="float" type="ieee_single"/>
    <field name="double" type="ieee_double"/>
  </union>

  <reg name="ft0" bitsize="64" type="riscv_double"/>
  <reg name="ft1" bitsize="64" type="riscv_double"/>
  <reg name="ft2" bitsize="64" type="riscv_double"/>
  <reg name="ft3" bitsize="64" type="riscv_double"/>
  <reg name="ft4" bitsize="64" type="riscv_double"/>
  <reg name="ft5" bitsize="64" type="riscv_double"/>
  <reg name="ft6" bitsize="64" type="riscv_double"/>
  <reg name="ft7" bitsize="64" type="riscv_double"/>
  <reg name="fs0" bitsize="64" type="riscv_double"/>
  <reg name="fs1" bitsize="64" type="riscv_double"/>
  <reg name="fa0" bitsize="64" type="riscv_double"/>
  <reg name="fa1" bitsize="64" type="riscv_double"/>
  <reg name="fa2" bitsize="64" type="riscv_double"/>
  <reg name="fa3" bitsize="64" type="riscv_double"/>
  <reg name="fa4" bitsize="64" type="riscv_double"/>
  <reg name="fa5" bitsize="64" type="riscv_double"/>
  <reg name="fa6" bitsize="64" type="riscv_double"/>
  <reg name="fa7" bitsize="64" type="riscv_double"/>
  <reg name="fs2" bitsize="64" type="riscv_double"/>
  <reg name="fs3" bitsize="64" type="riscv_double"/>
  <reg name="fs4" bitsize="64" type="riscv_double"/>
  <reg name="fs5" bitsize="64" type="riscv_double"/>
  <reg name="fs6" bitsize="64" type="riscv_double"/>
  <reg name="fs7" bitsize="64" type="riscv_double"/>
  <reg name="fs8" bitsize="64" type="riscv_double"/>
  <reg name="fs9" bitsize="64" type="riscv_double"/>
  <reg name="fs10" bitsize="64" type="riscv_double"/>
  <reg name="fs11" bitsize="64" type="riscv_double"/>
  <reg name="ft8" bitsize="64" type="riscv_double"/>
  <reg name="ft9" bitsize="64" type="riscv_double"/>
  <reg name="ft10" bitsize="64" type="riscv_double"/>
  <reg name="ft11" bitsize="64" type="riscv_double"/>
</feature>
